#!/bin/bash

# CBBC Application Deployment Verification Script
# This script verifies that both the application and Selenium services are working correctly

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
COMPOSE_FILE="${COMPOSE_FILE:-docker-compose.prod.yml}"
PROJECT_NAME="${PROJECT_NAME:-cbbc}"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if containers are running
check_containers() {
    log_info "Checking container status..."
    
    local compose_cmd="docker-compose"
    if ! command -v docker-compose &> /dev/null; then
        compose_cmd="docker compose"
    fi
    
    local containers=$(${compose_cmd} -f "${COMPOSE_FILE}" -p "${PROJECT_NAME}" ps --format json 2>/dev/null || echo "[]")
    
    if [ "$containers" = "[]" ] || [ -z "$containers" ]; then
        log_error "No containers found. Please deploy the application first:"
        log_info "  ./scripts/deploy.sh"
        return 1
    fi
    
    # Check individual container status
    local app_status=$(docker inspect cbbc-app --format='{{.State.Status}}' 2>/dev/null || echo "not found")
    local selenium_status=$(docker inspect cbbc-selenium --format='{{.State.Status}}' 2>/dev/null || echo "not found")
    
    if [ "$app_status" = "running" ]; then
        log_success "cbbc-app container is running"
    else
        log_error "cbbc-app container is not running (status: $app_status)"
        return 1
    fi
    
    if [ "$selenium_status" = "running" ]; then
        log_success "cbbc-selenium container is running"
    else
        log_error "cbbc-selenium container is not running (status: $selenium_status)"
        return 1
    fi
    
    return 0
}

# Test application health
test_app_health() {
    log_info "Testing application health..."
    
    local response=$(curl -s -w "%{http_code}" -o /tmp/health_response http://localhost:8000/api/market-status 2>/dev/null || echo "000")
    
    if [ "$response" = "200" ]; then
        log_success "Application health check passed"
        log_info "Response: $(cat /tmp/health_response 2>/dev/null || echo 'No response body')"
        return 0
    else
        log_error "Application health check failed (HTTP $response)"
        return 1
    fi
}

# Test Selenium health
test_selenium_health() {
    log_info "Testing Selenium health..."
    
    local response=$(curl -s -w "%{http_code}" -o /tmp/selenium_response http://localhost:4444/wd/hub/status 2>/dev/null || echo "000")
    
    if [ "$response" = "200" ]; then
        log_success "Selenium health check passed"
        local ready=$(cat /tmp/selenium_response 2>/dev/null | grep -o '"ready":[^,]*' | cut -d':' -f2 || echo "unknown")
        log_info "Selenium ready status: $ready"
        return 0
    else
        log_error "Selenium health check failed (HTTP $response)"
        return 1
    fi
}

# Test full functionality
test_full_functionality() {
    log_info "Testing full functionality (web scraping)..."
    log_warning "This will trigger a real data download from AASTOCKS..."
    
    read -p "Do you want to proceed with the full functionality test? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "Skipping full functionality test"
        return 0
    fi
    
    log_info "Triggering manual update..."
    local response=$(curl -s -w "%{http_code}" -X POST -o /tmp/update_response http://localhost:8000/api/manual-update 2>/dev/null || echo "000")
    
    if [ "$response" = "200" ]; then
        log_success "Manual update completed successfully"
        log_info "Response: $(cat /tmp/update_response 2>/dev/null || echo 'No response body')"
        
        # Check if files were created
        log_info "Checking downloaded files..."
        local files=$(docker exec cbbc-app ls -la /app/downloads/ 2>/dev/null | grep -E "\.(xlsx|xls)" || echo "")
        if [ -n "$files" ]; then
            log_success "Excel files found in download directory"
            echo "$files"
        else
            log_warning "No Excel files found in download directory"
        fi
        
        return 0
    else
        log_error "Manual update failed (HTTP $response)"
        log_info "Response: $(cat /tmp/update_response 2>/dev/null || echo 'No response body')"
        return 1
    fi
}

# Test VNC access
test_vnc_access() {
    log_info "Testing VNC access..."
    
    local response=$(curl -s -w "%{http_code}" -o /dev/null http://localhost:7900 2>/dev/null || echo "000")
    
    if [ "$response" = "200" ]; then
        log_success "VNC access is available at http://localhost:7900"
        return 0
    else
        log_error "VNC access failed (HTTP $response)"
        return 1
    fi
}

# Show deployment summary
show_summary() {
    log_info "Deployment Summary:"
    echo "  📱 Main Application: http://localhost:8000"
    echo "  🔧 API Endpoints: http://localhost:8000/api/"
    echo "  🕷️  Selenium Grid: http://localhost:4444"
    echo "  🖥️  VNC Debug: http://localhost:7900"
    echo ""
    log_info "Available API endpoints:"
    echo "  GET  /api/market-status     - Check market status"
    echo "  POST /api/manual-update     - Trigger data update"
    echo "  POST /api/toggle-auto-update - Toggle auto updates"
    echo "  POST /api/set-update-interval - Set update interval"
}

# Main execution
main() {
    log_info "Starting CBBC Application deployment verification..."
    echo ""
    
    local tests_passed=0
    local tests_total=4
    
    # Run tests
    if check_containers; then
        ((tests_passed++))
    fi
    
    if test_app_health; then
        ((tests_passed++))
    fi
    
    if test_selenium_health; then
        ((tests_passed++))
    fi
    
    if test_vnc_access; then
        ((tests_passed++))
    fi
    
    echo ""
    log_info "Basic tests completed: $tests_passed/$tests_total passed"
    
    if [ $tests_passed -eq $tests_total ]; then
        log_success "All basic tests passed! 🎉"
        echo ""
        test_full_functionality
        echo ""
        show_summary
    else
        log_error "Some tests failed. Please check the logs and troubleshoot."
        echo ""
        log_info "Troubleshooting tips:"
        echo "  1. Check container logs: docker logs cbbc-app"
        echo "  2. Check container logs: docker logs cbbc-selenium"
        echo "  3. Verify deployment: ./scripts/deploy.sh"
        echo "  4. Check DEPLOYMENT.md for detailed troubleshooting"
        exit 1
    fi
}

# Help function
show_help() {
    echo "CBBC Application Deployment Verification Script"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -f, --compose-file FILE    Docker Compose file (default: docker-compose.prod.yml)"
    echo "  -p, --project PROJECT      Project name (default: cbbc)"
    echo "  -h, --help                 Show this help message"
    echo ""
    echo "This script verifies that the CBBC application stack is deployed correctly"
    echo "and all services are functioning properly."
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -f|--compose-file)
            COMPOSE_FILE="$2"
            shift 2
            ;;
        -p|--project)
            PROJECT_NAME="$2"
            shift 2
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Run main function
main

# Cleanup
rm -f /tmp/health_response /tmp/selenium_response /tmp/update_response 2>/dev/null || true
