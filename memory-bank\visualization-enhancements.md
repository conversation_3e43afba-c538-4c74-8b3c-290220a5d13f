# Visualization Enhancements Documentation

## Overview
This document details the enhanced visualization features implemented for the CBBC Chart Application, specifically focusing on side-by-side chart improvements with user-configurable x-axis scaling.

## Enhanced Side-by-Side Charts

### 1. User-Configurable Scale Ratio
**Implementation**: User-selectable x-axis scale ratios for comparing OS_VAL (剩餘價值) and TURN_AMT (當日成交) data with different magnitudes.

**Features**:
- **Radio Button Control**: Allows users to select scale ratios (1:1, 10:1, 50:1, 100:1, 200:1, Auto)
- **Dynamic UI**: Scale ratio control only appears when side-by-side chart is selected
- **X-axis Scaling**: TURN_AMT chart x-axis = OS_VAL chart x-axis × ratio
- **Intelligent Auto-Detection**: Automatically selects optimal ratio based on data characteristics

**Benefits**:
- Handles extreme data magnitude differences (e.g., OS_VAL ~40M vs TURN_AMT ~2.5B)
- User has full control over visual comparison
- Maintains data integrity while improving readability
- Optimal for CBBC data where TURN_AMT is typically 50-100x larger than OS_VAL

### 2. Automatic Scale Detection
**Implementation**: Intelligent algorithm that automatically selects optimal scale ratios based on data characteristics.

**Features**:
- Calculates data ratio: `data_ratio = TURN_AMT_max / OS_VAL_max`
- Selects ratio to achieve 50-80% x-axis coverage for TURN_AMT data
- Provides console logging of scale decisions
- Adds scale information to chart title

**Algorithm**:
```python
if data_ratio >= 150:
    applied_ratio = 200
elif data_ratio >= 50:
    applied_ratio = 100  # Optimal for typical CBBC data
elif data_ratio >= 25:
    applied_ratio = 50
elif data_ratio >= 7:
    applied_ratio = 10
else:
    applied_ratio = 1
```

**Example**: For OS_VAL ~40M and TURN_AMT ~2.5B (ratio 62.5), auto-selects 100:1 scale.

### 3. Dual Y-Axis Display
**Implementation**: Y-axis labels displayed on both left and right sides of side-by-side charts.

**Features**:
- Left chart: Y-axis on left side (standard)
- Right chart: Y-axis on right side for better readability
- Both axes maintain consistent formatting:
  - `tickmode='linear'`
  - `tickformat='00000'`
  - `dtick=200`

**Configuration**:
```python
# Left y-axis
fig.update_yaxes(side='left', row=1, col=1)
# Right y-axis
fig.update_yaxes(side='right', showticklabels=True, row=1, col=2)
```

### 4. Enhanced Horizontal Reference Lines
**Implementation**: HSI price reference lines (High, Close, Low) properly scaled for each subplot.

**Features**:
- Left subplot: Lines scaled to OS_VAL x-axis range
- Right subplot: Lines scaled to TURN_AMT x-axis range (with applied ratio)
- Maintains visual consistency across both charts
- Price annotation positioned appropriately on right chart

### 5. User Interface Controls
**Implementation**: Dynamic UI controls that appear only when relevant.

**Features**:
- **Scale Ratio Control**: Only visible when "左右比對" (side-by-side) is selected
- **Radio Button Options**: 1:1, 10:1, 50:1, 100:1, 200:1, Auto
- **Default Setting**: Auto (intelligent selection)
- **Visual Feedback**: Chart title shows applied ratio when ≠ 1:1

## Technical Implementation

### Modified Functions
- `plot_graph()` in `app.py`: Enhanced side-by-side chart generation

### Key Variables
- `scale_ratio`: Ratio between OS_VAL and TURN_AMT maximum values
- `needs_scale_adjustment`: Boolean flag for applying scaling
- `adjusted_max_os`: Adjusted maximum for OS_VAL chart display
- `adjusted_max_turn`: Adjusted maximum for TURN_AMT chart display

### Console Output
The system now provides informative logging:
```
Scale ratio (OS_VAL/TURN_AMT): 2.34
Scaling down OS_VAL display: 1,234,567 -> 567,890
```

## User Experience Improvements

### Visual Benefits
1. **Better Proportionality**: Charts are visually balanced regardless of data magnitude differences
2. **Enhanced Readability**: Y-axis labels on both sides improve data interpretation
3. **Clear Scale Information**: Title indicates when scale adjustment has been applied
4. **Consistent Reference Lines**: HSI price lines properly scaled for each chart

### Chart Title Enhancement
When scale adjustment is applied, the title includes ratio information:
```
恒指牛熊證 街貨回收價分佈及當天成交 (並排) 2024-01-15 @14:30 [比例調整: 2.3:1]
```

## Future Enhancements
- User-configurable scale adjustment thresholds
- Manual scale override options
- Additional scaling algorithms (logarithmic, percentile-based)
- Scale adjustment for other chart types
