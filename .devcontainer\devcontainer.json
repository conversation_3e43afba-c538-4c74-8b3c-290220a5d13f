{
	"name": "CBBC2",
	"dockerComposeFile": "docker-compose.yaml",
	"service": "devcontainer",
	"workspaceFolder": "/workspace",
	"containerEnv": {
		"PIPENV_VENV_IN_PROJECT": "1"
	},
	{
		"environment": {
			"LANG": "en_US.UTF-8"
			// "LC_ALL": "en_US.UTF-8"
		}
	},
	"customizations": {
		"vscode": {
			"extensions": [
				"ms-azuretools.vscode-docker",
				"ms-python.python"
			]
		}
	},
	"postStartCommand": "git config --global --add safe.directory ${containerWorkspaceFolder}"
}