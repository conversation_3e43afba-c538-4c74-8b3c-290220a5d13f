#!/bin/bash

# CBBC Application Docker Build Script
# This script builds the Docker image for production deployment

set -e  # Exit on any error

# Configuration
DOCKER_USERNAME="${DOCKER_USERNAME:-breyton}"
IMAGE_NAME="${IMAGE_NAME:-cbbc-app}"
VERSION="${VERSION:-latest}"
DOCKERFILE="${DOCKERFILE:-Dockerfile}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        log_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
    log_info "Docker is running"
}

# Build the Docker image
build_image() {
    local full_image_name="${DOCKER_USERNAME}/${IMAGE_NAME}:${VERSION}"
    
    log_info "Building Docker image: ${full_image_name}"
    log_info "Using Dockerfile: ${DOCKERFILE}"
    
    # Build the image
    if docker build -t "${full_image_name}" -f "${DOCKERFILE}" .; then
        log_success "Docker image built successfully: ${full_image_name}"
        
        # Also tag as latest if version is not latest
        if [ "${VERSION}" != "latest" ]; then
            docker tag "${full_image_name}" "${DOCKER_USERNAME}/${IMAGE_NAME}:latest"
            log_info "Tagged as latest: ${DOCKER_USERNAME}/${IMAGE_NAME}:latest"
        fi
        
        return 0
    else
        log_error "Failed to build Docker image"
        return 1
    fi
}

# Show image information
show_image_info() {
    local full_image_name="${DOCKER_USERNAME}/${IMAGE_NAME}:${VERSION}"
    
    log_info "Image information:"
    docker images "${DOCKER_USERNAME}/${IMAGE_NAME}" --format "table {{.Repository}}\t{{.Tag}}\t{{.ID}}\t{{.CreatedAt}}\t{{.Size}}"
    
    log_info "Image layers:"
    docker history "${full_image_name}" --format "table {{.CreatedBy}}\t{{.Size}}"
}

# Main execution
main() {
    log_info "Starting CBBC Application Docker build process"
    log_info "Configuration:"
    log_info "  Docker Username: ${DOCKER_USERNAME}"
    log_info "  Image Name: ${IMAGE_NAME}"
    log_info "  Version: ${VERSION}"
    log_info "  Dockerfile: ${DOCKERFILE}"
    
    # Check prerequisites
    check_docker
    
    # Build the image
    if build_image; then
        show_image_info
        log_success "Build process completed successfully!"
        log_info "Next steps:"
        log_info "  1. Test with full stack: docker-compose -f docker-compose.prod.yml --env-file .env.prod up -d"
        log_info "  2. Test app only: docker run --rm -p 8000:8000 ${DOCKER_USERNAME}/${IMAGE_NAME}:${VERSION}"
        log_info "     Note: App-only testing will not include Selenium functionality"
        log_info "  3. Push to DockerHub: ./scripts/push.sh"
        log_info ""
        log_warning "This application requires Selenium for web scraping functionality."
        log_warning "Use docker-compose for full functionality including data downloads."
    else
        log_error "Build process failed!"
        exit 1
    fi
}

# Help function
show_help() {
    echo "CBBC Application Docker Build Script"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -u, --username USERNAME    DockerHub username (default: your-dockerhub-username)"
    echo "  -n, --name IMAGE_NAME      Image name (default: cbbc-app)"
    echo "  -v, --version VERSION      Image version/tag (default: latest)"
    echo "  -f, --dockerfile FILE      Dockerfile to use (default: Dockerfile)"
    echo "  -h, --help                 Show this help message"
    echo ""
    echo "Environment variables:"
    echo "  DOCKER_USERNAME           DockerHub username"
    echo "  IMAGE_NAME                Image name"
    echo "  VERSION                   Image version/tag"
    echo "  DOCKERFILE                Dockerfile to use"
    echo ""
    echo "Examples:"
    echo "  $0                                           # Build with defaults"
    echo "  $0 -u myusername -v v1.0.0                  # Build with specific username and version"
    echo "  DOCKER_USERNAME=myuser VERSION=v1.0.0 $0    # Build with environment variables"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -u|--username)
            DOCKER_USERNAME="$2"
            shift 2
            ;;
        -n|--name)
            IMAGE_NAME="$2"
            shift 2
            ;;
        -v|--version)
            VERSION="$2"
            shift 2
            ;;
        -f|--dockerfile)
            DOCKERFILE="$2"
            shift 2
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Validate required parameters
if [ "${DOCKER_USERNAME}" = "your-dockerhub-username" ]; then
    log_warning "Using default DockerHub username. Please set DOCKER_USERNAME environment variable or use -u option."
fi

# Run main function
main
