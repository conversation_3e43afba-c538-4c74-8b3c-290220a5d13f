#!/bin/bash
set -e

# Function to log messages
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

log "Starting CBBC application entrypoint..."

# Ensure data directories exist and have correct permissions
log "Setting up data directories..."

# Create directories if they don't exist
mkdir -p /app/data/logs
mkdir -p /app/instance
mkdir -p /app/downloads

# Fix ownership of mounted volumes
if [ "$(id -u)" = "0" ]; then
    # Running as root, fix permissions and switch to appuser
    log "Running as root, fixing permissions..."
    chown -R appuser:appuser /app/data /app/instance /app/logs
    # Make downloads directory accessible to both appuser (1000) and seluser (1200)
    chown -R 1000:1000 /app/downloads
    chmod -R 777 /app/downloads  # Allow both users to read/write
    log "Switching to appuser and executing command: $@"
    exec gosu appuser "$@"
else
    # Already running as non-root user
    log "Running as non-root user ($(id -u):$(id -g))"
    # Try to create directories with current user
    mkdir -p /app/data/logs /app/instance /app/downloads 2>/dev/null || true
    log "Executing command: $@"
    exec "$@"
fi
