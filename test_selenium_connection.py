#!/usr/bin/env python3
"""
Test script to verify Selenium container connectivity
before running the main scraper.
"""

import os
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from dotenv import load_dotenv

load_dotenv()

def test_selenium_connection():
    """Test the Selenium container connection"""
    
    selenium_hub_url = os.environ.get('SELENIUM_HUB_URL', 'http://localhost:4444/wd/hub')
    print(f"Testing Selenium Hub connection: {selenium_hub_url}")
    
    # Setup Chrome options
    options = webdriver.ChromeOptions()
    options.add_argument('--ignore-ssl-errors=yes')
    options.add_argument('--ignore-certificate-errors')
    options.add_argument('--disable-dev-shm-usage')
    options.add_argument('--no-sandbox')
    options.add_argument('--disable-gpu')
    
    driver = None
    try:
        print("1. Initializing Chrome WebDriver...")
        driver = webdriver.Remote(
            command_executor=selenium_hub_url,
            options=options
        )
        print("   ✓ WebDriver initialized successfully")
        
        print("2. Testing basic navigation...")
        driver.get("https://www.google.com")
        print(f"   ✓ Successfully loaded: {driver.title}")
        
        print("3. Testing target HKEX domain...")
        driver.get("https://www.hkex.com.hk")
        time.sleep(3)
        print(f"   ✓ Successfully loaded HKEX: {driver.title}")
        
        print("4. Testing specific HKEX URL...")
        target_url = "https://www.hkex.com.hk/eng/stat/dmstat/dayrpt/dqe250718.htm"
        driver.get(target_url)
        time.sleep(5)  # Wait for page to load
        print(f"   ✓ Successfully loaded target page: {driver.title}")
        print(f"   Page source length: {len(driver.page_source):,} characters")
        
        # Check if page loaded properly (not error page)
        if "404" in driver.title.lower() or "not found" in driver.title.lower():
            print("   ⚠️  Warning: Page might not exist (404 error)")
        elif "error" in driver.title.lower():
            print("   ⚠️  Warning: Page returned an error")
        else:
            print("   ✓ Page loaded successfully")
        
        return True
        
    except Exception as e:
        print(f"   ✗ Error: {e}")
        return False
    finally:
        if driver:
            print("5. Cleaning up...")
            driver.quit()
            print("   ✓ WebDriver closed")

def main():
    """Main test function"""
    print("="*60)
    print("SELENIUM CONTAINER CONNECTION TEST")
    print("="*60)
    
    success = test_selenium_connection()
    
    print("\n" + "="*60)
    if success:
        print("✓ ALL TESTS PASSED")
        print("The Selenium container is working correctly.")
        print("You can now run the main scraper script.")
    else:
        print("✗ TESTS FAILED")
        print("Please check:")
        print("1. Selenium container is running: docker ps")
        print("2. Port 4444 is accessible: curl http://localhost:4444/wd/hub/status")
        print("3. Environment variable SELENIUM_HUB_URL is set correctly")
    print("="*60)

if __name__ == "__main__":
    main()
