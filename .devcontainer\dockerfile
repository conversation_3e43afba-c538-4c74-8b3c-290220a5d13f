FROM mcr.microsoft.com/devcontainers/python:3.11-bookworm
# WORKDIR /code
# COPY requirements.txt requirements.txt
# RUN pip install -r requirements.txt
# COPY . .
# CMD ["sleep", "infinity"]
# Set the locale
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    apt-utils \
    locales

RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    fonts-noto-cjk \
    fonts-wqy-microhei \
    fonts-wqy-zenhei

ENV PYTHONIOENCODING=utf-8