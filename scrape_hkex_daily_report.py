#!/usr/bin/env python3
"""
HKEX Daily Report HTML Scraper
Scrapes HTML content from HKEX daily reports using the cbbc-selenium container.

URL Format: https://www.hkex.com.hk/eng/stat/dmstat/dayrpt/dqe{YYMMDD}.htm
Example: https://www.hkex.com.hk/eng/stat/dmstat/dayrpt/dqe250718.htm

Usage:
    python scrape_hkex_daily_report.py
    python scrape_hkex_daily_report.py --date 250718
    python scrape_hkex_daily_report.py --url https://www.hkex.com.hk/eng/stat/dmstat/dayrpt/dqe250718.htm
"""

import time
import datetime as dt
import argparse
import os
import sys
from pathlib import Path
from selenium import webdriver
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import logging
from dotenv import load_dotenv
from bs4 import BeautifulSoup
import json

# Import logging configuration if available
try:
    from logging_config import setup_logging, get_logger, get_hk_time, format_hk_time
    USE_CUSTOM_LOGGING = True
except ImportError:
    USE_CUSTOM_LOGGING = False
    # Fallback logging setup
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

# Load environment variables
load_dotenv()

class HKEXDailyReportScraper:
    """HKEX Daily Report HTML Scraper using Selenium container"""
    
    def __init__(self):
        """Initialize the scraper with configuration from environment variables"""
        
        # Setup logging
        if USE_CUSTOM_LOGGING:
            setup_logging()
            self.logger = get_logger('hkex_scraper')
        else:
            self.logger = logging.getLogger('hkex_scraper')
        
        # Selenium configuration
        self.selenium_hub_url = os.environ.get('SELENIUM_HUB_URL', 'http://localhost:4444/wd/hub')
        self.logger.info(f"Selenium Hub URL: {self.selenium_hub_url}")
        
        # Output configuration
        self.output_path = os.environ.get('download_path', '/app/downloads/')
        if not os.path.exists(self.output_path):
            os.makedirs(self.output_path, exist_ok=True)
        self.logger.info(f"Output path: {self.output_path}")
        
        # WebDriver instance
        self.driver = None
        
    def get_hk_time(self):
        """Get current Hong Kong time"""
        if USE_CUSTOM_LOGGING:
            return get_hk_time()
        else:
            # Fallback implementation
            import pytz
            hk_tz = pytz.timezone('Asia/Hong_Kong')
            return dt.datetime.now(hk_tz)
    
    def format_hk_time(self, dt_obj=None):
        """Format Hong Kong time"""
        if USE_CUSTOM_LOGGING:
            return format_hk_time(dt_obj)
        else:
            if dt_obj is None:
                dt_obj = self.get_hk_time()
            return dt_obj.strftime('%Y-%m-%d %H:%M:%S %Z')
    
    def setup_webdriver(self):
        """Setup Chrome WebDriver with optimal configuration for scraping"""
        self.logger.info("Setting up Chrome WebDriver")
        
        options = webdriver.ChromeOptions()
        
        # Basic security and SSL options
        options.add_argument('--ignore-ssl-errors=yes')
        options.add_argument('--ignore-certificate-errors')
        options.add_argument('--ignore-certificate-errors-spki-list')
        options.add_argument('--ignore-ssl-errors-bypass')
        options.add_argument('--allow-running-insecure-content')
        
        # Docker environment options
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--disable-extensions')
        options.add_argument('--disable-background-timer-throttling')
        options.add_argument('--disable-backgrounding-occluded-windows')
        options.add_argument('--disable-renderer-backgrounding')
        
        # Window and display options
        options.add_argument('--window-size=1920,1080')
        options.add_argument('--start-maximized')
        
        # User agent to appear as regular browser
        options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
        
        # Download preferences (in case page has downloadable content)
        prefs = {
            "download.default_directory": "/home/<USER>/Downloads",
            "download.prompt_for_download": False,
            "download.directory_upgrade": True,
            "safebrowsing.enabled": False
        }
        options.add_experimental_option("prefs", prefs)
        
        try:
            self.logger.debug(f"Connecting to Selenium Hub at: {self.selenium_hub_url}")
            self.driver = webdriver.Remote(
                command_executor=self.selenium_hub_url,
                options=options
            )
            self.logger.info("WebDriver initialized successfully")
            return True
        except Exception as e:
            self.logger.error(f"Failed to initialize WebDriver: {e}")
            return False
    
    def build_hkex_url(self, date_str=None):
        """
        Build HKEX daily report URL from date string
        
        Args:
            date_str (str): Date in format YYMMDD (e.g., "250718" for 2025-07-18)
                           If None, uses today's date
        
        Returns:
            str: Complete HKEX URL
        """
        if date_str is None:
            # Use today's date in HK timezone
            hk_time = self.get_hk_time()
            date_str = hk_time.strftime('%y%m%d')
        
        # Validate date format
        if not date_str.isdigit() or len(date_str) != 6:
            raise ValueError(f"Date must be in YYMMDD format, got: {date_str}")
        
        base_url = "https://www.hkex.com.hk/eng/stat/dmstat/dayrpt/dqe{}.htm"
        url = base_url.format(date_str)
        
        self.logger.info(f"Built HKEX URL: {url}")
        return url
    
    def scrape_page(self, url, timeout=30):
        """
        Scrape the HTML content from the specified URL
        
        Args:
            url (str): URL to scrape
            timeout (int): Timeout in seconds for page load
            
        Returns:
            dict: Scraped data including HTML content and metadata
        """
        if not self.driver:
            raise RuntimeError("WebDriver not initialized. Call setup_webdriver() first.")
        
        self.logger.info(f"Starting to scrape: {url}")
        start_time = self.get_hk_time()
        
        try:
            # Navigate to the page
            self.logger.debug("Loading page...")
            self.driver.get(url)
            
            # Wait for page to load completely
            wait = WebDriverWait(self.driver, timeout)
            
            # Wait for body element to be present
            wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
            
            # Additional wait for dynamic content
            time.sleep(3)
            
            # Get page title
            page_title = self.driver.title
            self.logger.info(f"Page loaded successfully. Title: {page_title}")
            
            # Get the full HTML content
            html_content = self.driver.page_source
            
            # Parse with BeautifulSoup for better structure
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Extract key information
            scraped_data = {
                'url': url,
                'title': page_title,
                'scraped_at': self.format_hk_time(start_time),
                'html_content': html_content,
                'text_content': soup.get_text(strip=True),
                'page_size': len(html_content),
                'has_tables': len(soup.find_all('table')) > 0,
                'table_count': len(soup.find_all('table')),
                'metadata': {
                    'user_agent': self.driver.execute_script("return navigator.userAgent;"),
                    'page_url': self.driver.current_url,
                    'page_encoding': soup.original_encoding if hasattr(soup, 'original_encoding') else 'Unknown'
                }
            }
            
            # Look for specific HKEX data structures
            self._extract_hkex_specific_data(soup, scraped_data)
            
            self.logger.info(f"Successfully scraped {len(html_content)} characters from page")
            self.logger.info(f"Found {scraped_data['table_count']} tables in the page")
            
            return scraped_data
            
        except TimeoutException:
            self.logger.error(f"Timeout while loading page: {url}")
            raise
        except Exception as e:
            self.logger.error(f"Error scraping page {url}: {e}")
            raise
    
    def _extract_hkex_specific_data(self, soup, scraped_data):
        """Extract HKEX-specific data structures from the page"""
        
        # Look for data tables
        tables = soup.find_all('table')
        if tables:
            scraped_data['tables'] = []
            for i, table in enumerate(tables):
                table_data = {
                    'index': i,
                    'rows': len(table.find_all('tr')),
                    'has_headers': bool(table.find('th')),
                    'class': table.get('class', []),
                    'id': table.get('id', '')
                }
                
                # Extract table headers if present
                headers = table.find_all('th')
                if headers:
                    table_data['headers'] = [th.get_text(strip=True) for th in headers]
                
                scraped_data['tables'].append(table_data)
        
        # Look for specific HKEX indicators
        # Date indicators
        date_elements = soup.find_all(string=lambda text: text and any(
            keyword in text.lower() for keyword in ['date', 'trading day', 'market day']
        ))
        if date_elements:
            scraped_data['date_mentions'] = [elem.strip() for elem in date_elements[:5]]
        
        # Market data indicators
        market_indicators = soup.find_all(string=lambda text: text and any(
            keyword in text.lower() for keyword in ['turnover', 'volume', 'shares', 'value', 'hk$']
        ))
        if market_indicators:
            scraped_data['market_data_mentions'] = len(market_indicators)
    
    def save_data(self, scraped_data, filename=None):
        """
        Save scraped data to files
        
        Args:
            scraped_data (dict): Data from scrape_page()
            filename (str): Base filename (without extension)
            
        Returns:
            dict: Paths to saved files
        """
        if filename is None:
            timestamp = self.get_hk_time().strftime('%Y%m%d_%H%M%S')
            filename = f"hkex_daily_report_{timestamp}"
        
        saved_files = {}
        
        try:
            # Save HTML content
            html_path = os.path.join(self.output_path, f"{filename}.html")
            with open(html_path, 'w', encoding='utf-8') as f:
                f.write(scraped_data['html_content'])
            saved_files['html'] = html_path
            self.logger.info(f"HTML content saved to: {html_path}")
            
            # Save metadata as JSON
            metadata = {k: v for k, v in scraped_data.items() if k not in ['html_content']}
            json_path = os.path.join(self.output_path, f"{filename}_metadata.json")
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, indent=2, ensure_ascii=False)
            saved_files['metadata'] = json_path
            self.logger.info(f"Metadata saved to: {json_path}")
            
            # Save plain text content
            text_path = os.path.join(self.output_path, f"{filename}.txt")
            with open(text_path, 'w', encoding='utf-8') as f:
                f.write(f"URL: {scraped_data['url']}\n")
                f.write(f"Title: {scraped_data['title']}\n")
                f.write(f"Scraped at: {scraped_data['scraped_at']}\n")
                f.write(f"Page size: {scraped_data['page_size']} characters\n")
                f.write(f"Tables found: {scraped_data['table_count']}\n")
                f.write("\n" + "="*80 + "\n")
                f.write("TEXT CONTENT:\n")
                f.write("="*80 + "\n\n")
                f.write(scraped_data['text_content'])
            saved_files['text'] = text_path
            self.logger.info(f"Text content saved to: {text_path}")
            
            return saved_files
            
        except Exception as e:
            self.logger.error(f"Error saving data: {e}")
            raise
    
    def cleanup(self):
        """Clean up WebDriver resources"""
        if self.driver:
            try:
                self.logger.debug("Closing WebDriver")
                self.driver.quit()
                self.driver = None
                self.logger.info("WebDriver closed successfully")
            except Exception as e:
                self.logger.warning(f"Error closing WebDriver: {e}")

def main():
    """Main function to run the scraper"""
    parser = argparse.ArgumentParser(description='Scrape HKEX Daily Report HTML')
    parser.add_argument('--date', type=str, help='Date in YYMMDD format (e.g., 250718)')
    parser.add_argument('--url', type=str, help='Full URL to scrape (overrides --date)')
    parser.add_argument('--output', type=str, help='Output filename base (without extension)')
    parser.add_argument('--verbose', '-v', action='store_true', help='Enable verbose logging')
    
    args = parser.parse_args()
    
    # Set logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    scraper = HKEXDailyReportScraper()
    
    try:
        # Initialize WebDriver
        if not scraper.setup_webdriver():
            sys.exit(1)
        
        # Determine URL to scrape
        if args.url:
            url = args.url
            scraper.logger.info(f"Using provided URL: {url}")
        else:
            url = scraper.build_hkex_url(args.date)
        
        # Scrape the page
        scraped_data = scraper.scrape_page(url)
        
        # Save the data
        saved_files = scraper.save_data(scraped_data, args.output)
        
        # Print summary
        print(f"\n{'='*60}")
        print("SCRAPING COMPLETED SUCCESSFULLY")
        print(f"{'='*60}")
        print(f"URL: {scraped_data['url']}")
        print(f"Title: {scraped_data['title']}")
        print(f"Page size: {scraped_data['page_size']:,} characters")
        print(f"Tables found: {scraped_data['table_count']}")
        print(f"Scraped at: {scraped_data['scraped_at']}")
        print(f"\nFiles saved:")
        for file_type, path in saved_files.items():
            print(f"  {file_type.upper()}: {path}")
        print(f"{'='*60}")
        
    except KeyboardInterrupt:
        scraper.logger.info("Scraping interrupted by user")
        sys.exit(1)
    except Exception as e:
        scraper.logger.error(f"Scraping failed: {e}")
        sys.exit(1)
    finally:
        scraper.cleanup()

if __name__ == "__main__":
    main()
