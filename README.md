# HSI CBBC Market Dashboard

## Project Overview
A containerized Dash/Flask application for visualizing Hong Kong Stock Exchange (HKEX) Callable Bull/Bear Contracts (CBBC) data with automated web scraping and real-time market status integration.

## Architecture
The application uses a multi-container Docker architecture:
- **Main Application**: Dash/Flask web interface with data processing
- **Selenium Service**: Automated web scraping from AASTOCKS
- **Shared Storage**: Volume-based data exchange between containers

## File Structure
```
├── app.py                  # Main application with Dash UI and Flask server
├── saveCBBC_XLS.py         # Data processing and Excel generation module
├── get_xlsx.py            # Excel file reading and parsing utility
├── download_warrant_data.py # Warrant data download functionality
├── post_CBBC_XLS.py       # Excel data posting utility
├── requirements.txt       # Python dependencies
├── Procfile                # Deployment configuration
├── tests/                  # Test suite
│   ├── test.py
│   ├── test_market_status.py
│   └── ...                # Additional test files
├── download/              # Storage for generated images
├── src/main.py             # Core application logic (possibly for backend processing)
└── memory-bank/           # Data persistence and logging directory
```

## Key Features
- **Automated Data Collection**: Selenium-based web scraping from AASTOCKS
- **Real-time Market Status**: Integrates with market-clock.com for HKEX status detection
- **Auto-update Functionality**: Background workers for scheduled data refresh during market hours
- **Multiple Chart Types**:
  - Overlay chart (default view)
  - Side-by-side comparison
  - Turnover-only view
  - OS value-only view
- **Dynamic Scaling**: Automatic and manual scale ratio adjustment for side-by-side charts
- **REST API Endpoints**: For external integration and status control
- **Containerized Deployment**: Docker-based production deployment with health monitoring
- **Visual Debugging**: VNC access to Selenium browser for troubleshooting

## Dependencies
- dash==2.17.0
- flask==3.0.0
- yfinance==0.2.30
- pandas==2.2.2
- numpy==2.0.2
- sqlalchemy==2.0.3
- python-dotenv==1.0.1
- requests==2.32.3
- beautifulsoup4==4.12.3
- plotly==5.24.1
- kaleido==0.2.1

## Quick Start (Docker - Recommended)

### Production Deployment
```bash
# 1. Clone repository
git clone <your-repo-url>
cd cbbc-app

# 2. Set up environment
cp .env.example .env.prod
# Edit .env.prod with your configuration

# 3. Deploy the full stack
export DOCKER_USERNAME=your-dockerhub-username
./scripts/build.sh && ./scripts/push.sh && ./scripts/deploy.sh

# 4. Access the application
# Main App: http://localhost:8000
# Selenium VNC: http://localhost:7900 (for debugging)
```

### Development Setup
```bash
# For local development without Docker
1. Install dependencies: `pip install -r requirements.txt`
2. Set environment variables:
   - `platform=sqlite` for local SQLite database
   - `download_path=./download/` for local file storage
3. Run application: `python app.py`
4. Access dashboard at http://localhost:8050
```

## Deployment Options

### Docker Compose (Recommended)
Full-featured deployment with web scraping capabilities:
```bash
docker-compose -f docker-compose.prod.yml --env-file .env.prod up -d
```

### Single Container (Limited)
Web interface only (no web scraping):
```bash
docker run -p 8000:8000 -v cbbc_data:/app/data breyton/cbbc-app:latest
```

### Legacy Heroku
```bash
web: gunicorn --bind :$PORT --workers 1 --timeout 120 app:server
```

## API Endpoints
- `GET /api/market-status` - Retrieve current market status
- `POST /api/toggle-auto-update` - Control auto-update functionality
- `POST /api/set-update-interval` - Configure update intervals
- `POST /api/manual-update` - Trigger manual data refresh

## Data Flow
1. **User Request**: Manual update triggered via web interface or API
2. **Web Scraping**: Selenium navigates to AASTOCKS and downloads Excel file
3. **Data Processing**: `saveCBBC_XLS.py` processes downloaded Excel data
4. **Database Storage**: Processed data stored in SQLite database
5. **Visualization**: Dash frontend queries database and generates interactive charts
6. **File Output**: Excel files and charts saved to organized directory structure
7. **Background Tasks**: Market status worker schedules automatic updates during market hours

## Container Services

### cbbc-app (Main Application)
- **Port**: 8000
- **Health Check**: `GET /api/market-status`
- **Volumes**: `/app/data`, `/app/instance`, `/app/downloads`
- **Dependencies**: Requires Selenium service for data collection

### cbbc-selenium (Web Scraping)
- **Ports**: 4444 (Grid), 7900 (VNC)
- **Image**: `selenium/standalone-chrome:latest`
- **Purpose**: Automated browser for AASTOCKS data download
- **Debugging**: VNC access at http://localhost:7900

## Development Tools
- **Testing**: Comprehensive test suite in `/tests` directory
- **Data Storage**: JSON data files in root, Excel files in `/xls`
- **Logging**: Integrated with Python logging module
- **Threading**: Uses daemon threads for background tasks
