# CBBC Application Environment Configuration
# Copy this file to .env and modify the values as needed

# Application Configuration
APP_PORT=8000
FLASK_ENV=production

# Database Configuration
# Options: sqlite, postgresql
DATABASE_PLATFORM=sqlite

# PostgreSQL Configuration (if using postgresql)
# DATABASE_URL=postgresql://username:password@hostname:port/database_name
# POSTGRES_USER=cbbc_user
# POSTGRES_PASSWORD=your_secure_password
# POSTGRES_DB=cbbc_db
# POSTGRES_HOST=localhost
# POSTGRES_PORT=5432

# Data Storage Paths
OUT_PATH=/app/data
download_path=/app/downloads/

# Selenium Configuration
SELENIUM_PORT=4444
VNC_PORT=7900
SELENIUM_HUB_URL=http://selenium:4444/wd/hub

# Nginx Configuration (if using nginx profile)
NGINX_PORT=80
NGINX_SSL_PORT=443

# Application Settings
TZ=Asia/Hong_Kong
PYTHONUNBUFFERED=1

# Security Settings (set these in production)
# SECRET_KEY=your-secret-key-here
# FLASK_SECRET_KEY=your-flask-secret-key-here

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=detailed

# Auto-update Settings
AUTO_UPDATE_ENABLED=true
UPDATE_INTERVAL_MINUTES=15

# Market Hours (Hong Kong time)
MARKET_OPEN_HOUR=9
MARKET_OPEN_MINUTE=30
MARKET_CLOSE_HOUR=16
MARKET_CLOSE_MINUTE=0

# Performance Settings
GUNICORN_WORKERS=1
GUNICORN_TIMEOUT=120
GUNICORN_MAX_REQUESTS=1000

# Health Check Settings
HEALTH_CHECK_INTERVAL=30
HEALTH_CHECK_TIMEOUT=10
HEALTH_CHECK_RETRIES=3
