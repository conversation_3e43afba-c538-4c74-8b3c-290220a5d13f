#!/usr/bin/env python3
"""
Test script to verify DEBUG logging configuration for CBBC application
"""

from dotenv import load_dotenv
import os
import logging

# Load environment variables
load_dotenv()

# Import logging configuration
from logging_config import setup_logging, get_logger, get_hk_time, format_hk_time

def test_logging_setup():
    """Test the complete logging setup"""
    
    print("="*60)
    print("CBBC APPLICATION LOGGING TEST")
    print("="*60)
    
    # Show environment configuration
    log_level = os.environ.get('LOG_LEVEL', 'INFO')
    platform = os.environ.get('platform', 'unknown')
    
    print(f"LOG_LEVEL environment variable: {log_level}")
    print(f"Platform: {platform}")
    print(f"Current Hong Kong time: {format_hk_time()}")
    print()
    
    # Setup logging
    logs_path = os.path.join('.', 'logs')
    os.makedirs(logs_path, exist_ok=True)
    
    setup_logging(log_level=log_level, log_dir=logs_path)
    
    # Test main application logger
    print("Testing main application logger:")
    main_logger = logging.getLogger('main_app')
    main_logger.debug('🐛 DEBUG: This is a debug message')
    main_logger.info('ℹ️  INFO: Application starting up')
    main_logger.warning('⚠️  WARNING: This is a warning message')
    main_logger.error('❌ ERROR: This is an error message')
    print()
    
    # Test specialized loggers
    print("Testing specialized loggers:")
    
    # Market status worker logger
    market_logger = get_logger('market_status_worker', log_dir=logs_path, log_level=log_level)
    market_logger.debug('🐛 Market worker DEBUG: Checking market status')
    market_logger.info('ℹ️  Market worker INFO: Market is open')
    
    # Auto-update worker logger  
    auto_update_logger = get_logger('auto_update_worker', log_dir=logs_path, log_level=log_level)
    auto_update_logger.debug('🐛 Auto-update DEBUG: Starting background worker')
    auto_update_logger.info('ℹ️  Auto-update INFO: Background worker started')
    
    # HKEX scraper logger
    scraper_logger = get_logger('hkex_scraper', log_dir=logs_path, log_level=log_level)
    scraper_logger.debug('🐛 Scraper DEBUG: Initializing web driver')
    scraper_logger.info('ℹ️  Scraper INFO: Successfully scraped HKEX data')
    
    print()
    print("="*60)
    print("LOGGING TEST COMPLETED")
    print("="*60)
    print(f"Log files are stored in: {os.path.abspath(logs_path)}")
    print("Check the log files to verify that DEBUG messages are being written to disk.")
    
    # List log files
    try:
        log_files = [f for f in os.listdir(logs_path) if f.endswith('.log')]
        if log_files:
            print(f"Current log files: {', '.join(log_files)}")
        else:
            print("No log files found yet (they will be created on first message)")
    except Exception as e:
        print(f"Error listing log files: {e}")

if __name__ == '__main__':
    test_logging_setup()
