# CBBC Application - Production Ready

A production-ready Docker deployment of the CBBC (Callable Bull/Bear Contracts) market analysis application.

## 🚀 Quick Start

```bash
# 1. Set your DockerHub username
export DOCKER_USERNAME=your-dockerhub-username

# 2. Build, push, and deploy
./scripts/build.sh && ./scripts/push.sh && ./scripts/deploy.sh

# 3. Access the application
open http://localhost:8000
```

## 📋 What's Included

### Production Features
- ✅ **Multi-stage Docker build** for optimized image size
- ✅ **Non-root user** for enhanced security
- ✅ **Health checks** for container monitoring
- ✅ **Gunicorn WSGI server** for production performance
- ✅ **Nginx reverse proxy** (optional) with rate limiting
- ✅ **Environment-based configuration**
- ✅ **Persistent data volumes**
- ✅ **Automated deployment scripts**

### Application Components
- **CBBC Market Analysis**: Real-time market data visualization
- **Selenium WebDriver**: Automated data collection
- **SQLite/PostgreSQL**: Configurable database backend
- **Dash/Plotly**: Interactive web interface
- **Background Workers**: Automated data updates

## 🛠 Prerequisites

- Docker 20.10+
- Docker Compose 2.0+
- DockerHub account (for private repository)

## 📁 Key Files

| File | Purpose |
|------|---------|
| `Dockerfile` | Production Docker image definition |
| `docker-compose.prod.yml` | Production deployment configuration |
| `.env.example` | Environment variables template |
| `scripts/build.sh` | Build Docker image |
| `scripts/push.sh` | Push to DockerHub |
| `scripts/deploy.sh` | Deploy application |
| `DEPLOYMENT.md` | Comprehensive deployment guide |

## 🔧 Configuration

### Environment Setup

1. **Copy environment template**:
   ```bash
   cp .env.example .env
   ```

2. **Edit configuration**:
   ```bash
   # Required: Set your DockerHub username
   DOCKER_USERNAME=your-dockerhub-username
   
   # Optional: Database configuration
   DATABASE_PLATFORM=sqlite  # or postgresql
   
   # Optional: Application settings
   AUTO_UPDATE_ENABLED=true
   UPDATE_INTERVAL_MINUTES=15
   ```

### Database Options

#### SQLite (Default)
- **Best for**: Single-user deployments, development
- **Configuration**: No additional setup required
- **Data location**: `/app/instance/app.db`

#### PostgreSQL
- **Best for**: Production deployments, multiple users
- **Configuration**: Set `DATABASE_PLATFORM=postgresql` and database credentials
- **Requires**: Additional PostgreSQL container

## 🚢 Deployment Options

### Option 1: Full Stack (Recommended)
```bash
# Deploy with all services (app + selenium)
./scripts/deploy.sh
```

### Option 2: With Nginx Reverse Proxy
```bash
# Deploy with nginx for production
docker-compose -f docker-compose.prod.yml --profile with-nginx up -d
```

### Option 3: Custom Configuration
```bash
# Deploy with custom settings
./scripts/deploy.sh -u myusername -v v1.0.0 -f custom-compose.yml
```

## 📊 Monitoring

### Health Checks
```bash
# Application health
curl http://localhost:8000/api/market-status

# Container status
docker ps
```

### Logs
```bash
# View application logs
./scripts/deploy.sh logs app 50

# Follow logs in real-time
./scripts/deploy.sh logs app 0
```

### Performance
```bash
# Container resource usage
docker stats

# Application metrics
curl http://localhost:8000/api/market-status | jq
```

## 🔄 Updates

### Deploy New Version
```bash
# Build new version
./scripts/build.sh -v v1.1.0

# Push to DockerHub
./scripts/push.sh -v v1.1.0

# Deploy update
./scripts/deploy.sh -v v1.1.0
```

### Rollback
```bash
# Deploy previous version
./scripts/deploy.sh -v v1.0.0
```

## 💾 Backup & Restore

### Backup Data
```bash
# Create backup
docker run --rm -v cbbc_data:/data -v $(pwd):/backup alpine \
  tar czf /backup/cbbc-backup-$(date +%Y%m%d).tar.gz -C /data .
```

### Restore Data
```bash
# Restore from backup
docker run --rm -v cbbc_data:/data -v $(pwd):/backup alpine \
  tar xzf /backup/cbbc-backup-YYYYMMDD.tar.gz -C /data
```

## 🔒 Security

### Production Security Features
- Non-root container user
- Minimal base image (Python slim)
- Security headers via Nginx
- Rate limiting
- Environment-based secrets

### Security Checklist
- [ ] Change default secret keys
- [ ] Use HTTPS in production
- [ ] Restrict network access
- [ ] Regular security updates
- [ ] Monitor access logs

## 🐛 Troubleshooting

### Common Issues

**Container won't start**:
```bash
docker logs cbbc-app
```

**Database connection issues**:
```bash
docker exec cbbc-app python -c "from config import get_config; print(get_config().get_database_url())"
```

**Selenium not working**:
```bash
curl http://localhost:4444/wd/hub/status
```

### Getting Help

1. Check [DEPLOYMENT.md](DEPLOYMENT.md) for detailed troubleshooting
2. Review container logs: `./scripts/deploy.sh logs`
3. Verify configuration: `docker exec cbbc-app env`

## 📄 License

[Your License Here]

## 🤝 Contributing

[Your Contributing Guidelines Here]

---

**Need detailed instructions?** See [DEPLOYMENT.md](DEPLOYMENT.md) for comprehensive deployment documentation.
