#!/bin/bash

# Test script to verify the deployment configuration

set -e

echo "Testing CBBC Deployment Configuration..."

# Test 1: Check if .env.prod file has required variables
echo "Test 1: Checking .env.prod file..."
if [ -f ".env.prod" ]; then
    echo "✓ .env.prod file exists"
    
    # Check for required variables
    required_vars=("platform" "DATABASE_URL" "OUT_PATH" "SELENIUM_HUB_URL")
    for var in "${required_vars[@]}"; do
        if grep -q "^${var}=" .env.prod; then
            echo "✓ ${var} is defined in .env.prod"
        else
            echo "✗ ${var} is missing from .env.prod"
        fi
    done
else
    echo "✗ .env.prod file not found"
fi

# Test 2: Check docker-compose.prod.yml syntax
echo -e "\nTest 2: Checking docker-compose.prod.yml syntax..."
if docker-compose -f docker-compose.prod.yml config > /dev/null 2>&1; then
    echo "✓ docker-compose.prod.yml syntax is valid"
else
    echo "✗ docker-compose.prod.yml has syntax errors"
fi

# Test 3: Check if Dockerfile builds successfully
echo -e "\nTest 3: Testing Dockerfile build..."
if docker build -t cbbc-test:latest . > /dev/null 2>&1; then
    echo "✓ Dockerfile builds successfully"
    docker rmi cbbc-test:latest > /dev/null 2>&1
else
    echo "✗ Dockerfile build failed"
fi

# Test 4: Check deployment scripts
echo -e "\nTest 4: Checking deployment scripts..."
scripts=("build.sh" "push.sh" "deploy.sh")
for script in "${scripts[@]}"; do
    if [ -f "scripts/${script}" ] && [ -x "scripts/${script}" ]; then
        echo "✓ scripts/${script} exists and is executable"
    else
        echo "✗ scripts/${script} is missing or not executable"
    fi
done

echo -e "\nTest completed!"
echo "To run the application with fixed configuration:"
echo "docker run --rm -p 8000:8000 --env-file .env.prod breyton/cbbc-app:latest"
