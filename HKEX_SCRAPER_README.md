# HKEX Daily Report HTML Scraper

This directory contains Python scripts to scrape HTML content from HKEX (Hong Kong Exchange) daily reports using the cbbc-selenium container.

## Files Created

1. **`scrape_hkex_daily_report.py`** - Main comprehensive scraper class
2. **`scrape_hkex_simple.py`** - Simple script for the specific URL you mentioned
3. **`test_selenium_connection.py`** - Test script to verify Selenium container connectivity

## Target URL

The scraper is designed to work with HKEX daily report URLs in this format:
```
https://www.hkex.com.hk/eng/stat/dmstat/dayrpt/dqe{YYMMDD}.htm
```

Example: `https://www.hkex.com.hk/eng/stat/dmstat/dayrpt/dqe250718.htm`

## Prerequisites

1. **Selenium Container Running**: The cbbc-selenium container must be running
2. **Docker Compose**: Use the existing docker-compose.prod.yml setup
3. **Network Connectivity**: Both containers must be on the same Docker network

## Quick Start

### 1. Deploy the Application Stack

```bash
# If not already running, deploy the full stack
./scripts/deploy.sh
```

### 2. Verify Selenium Container

```bash
# Check container status
docker ps | grep selenium

# Test Selenium connectivity
curl http://localhost:4444/wd/hub/status
```

### 3. Run the Test Script (Recommended First)

```bash
# Inside the cbbc-app container
docker exec -it cbbc-app python test_selenium_connection.py
```

### 4. Run the Simple Scraper

```bash
# Scrape the specific URL you mentioned
docker exec -it cbbc-app python scrape_hkex_simple.py
```

### 5. Run the Advanced Scraper

```bash
# Scrape with custom date (YYMMDD format)
docker exec -it cbbc-app python scrape_hkex_daily_report.py --date 250718

# Scrape with custom URL
docker exec -it cbbc-app python scrape_hkex_daily_report.py --url "https://www.hkex.com.hk/eng/stat/dmstat/dayrpt/dqe250718.htm"

# Verbose output
docker exec -it cbbc-app python scrape_hkex_daily_report.py --date 250718 --verbose
```

## Output Files

The scraper saves three types of files in the `/app/downloads/` directory:

1. **HTML File** (`.html`) - Complete HTML source of the page
2. **Metadata File** (`_metadata.json`) - Structured data about the scraping session
3. **Text File** (`.txt`) - Plain text content extracted from the page

Example output:
```
/app/downloads/
├── hkex_dqe250718.html              # Raw HTML content
├── hkex_dqe250718_metadata.json     # Structured metadata
└── hkex_dqe250718.txt               # Plain text content
```

## Features

### Comprehensive Data Extraction
- Complete HTML source
- Page metadata (title, size, tables count)
- Plain text content
- Table structure analysis
- HKEX-specific data indicators

### Error Handling
- Robust timeout handling
- SSL certificate bypass for HKEX
- Automatic retry mechanisms
- Detailed logging

### Docker Integration
- Uses existing selenium container
- Shares volumes with main app
- Environment variable configuration
- Integrated with existing logging system

## Environment Variables

The scraper uses these environment variables (already configured in docker-compose.prod.yml):

```bash
SELENIUM_HUB_URL=http://selenium:4444/wd/hub  # Selenium connection
download_path=/app/downloads/                  # Output directory
```

## Troubleshooting

### Common Issues

1. **Selenium Connection Failed**
   ```bash
   # Check if selenium container is running
   docker ps | grep selenium
   
   # Check selenium logs
   docker logs cbbc-selenium
   
   # Test connectivity
   docker exec cbbc-app curl http://selenium:4444/wd/hub/status
   ```

2. **Permission Issues**
   ```bash
   # Check download directory permissions
   docker exec cbbc-app ls -la /app/downloads/
   
   # Fix if needed
   docker exec --user root cbbc-app chmod 777 /app/downloads/
   ```

3. **Network Issues**
   ```bash
   # Verify containers are on same network
   docker network ls
   docker inspect cbbc_cbbc_network
   ```

### Debug with VNC

Access the Selenium browser visually for debugging:
```
http://localhost:7900
```
(No password required)

## Script Details

### scrape_hkex_daily_report.py
- Full-featured scraper class
- Command-line interface
- Automatic date handling
- Comprehensive error handling
- Structured output

### scrape_hkex_simple.py
- Simplified version for quick testing
- Hard-coded URL for your specific request
- Clear step-by-step output
- Good for demonstrations

### test_selenium_connection.py
- Connectivity tester
- Tests basic navigation
- Verifies HKEX domain access
- Validates target URL accessibility

## Integration with Existing System

The scraper integrates seamlessly with your existing CBBC application:

1. **Uses Same Logging**: Integrates with your `logging_config.py`
2. **Same Environment**: Uses existing environment variables
3. **Same Volumes**: Saves to shared `/app/downloads/` directory
4. **Same Network**: Uses the cbbc_network Docker network

## Example Usage Session

```bash
# 1. Check system status
./scripts/deploy.sh status

# 2. Test connectivity
docker exec -it cbbc-app python test_selenium_connection.py

# 3. Run scraper
docker exec -it cbbc-app python scrape_hkex_simple.py

# 4. Check output
docker exec cbbc-app ls -la /app/downloads/

# 5. View scraped content
docker exec cbbc-app head -20 /app/downloads/hkex_dqe250718.txt
```

This scraper follows the same patterns as your existing `saveCBBC_XLS.py` script and integrates well with your production Docker environment.
