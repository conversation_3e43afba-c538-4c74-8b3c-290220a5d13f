# CBBC Chart Application Project Brief

## Project Overview
This is a Dash web application for visualizing Hong Kong Stock Exchange CBBC (Callable Bull/Bear Contracts) data. The application provides interactive charts and data tables for analyzing CBBC market data.

## Core Requirements
1. **Interactive Chart Visualization**: Multiple chart types for CBBC data analysis
   - 單圖比對 (overlay): Overlaid bar charts showing remaining value and daily turnover
   - 左右比對 (sidebyside): Side-by-side comparison charts
   - 當日成交金額 (turnamt): Daily turnover amount only
   - 剩餘價值 (osval): Outstanding value only

2. **Data Refresh Functionality**: Manual refresh button to update data from database

3. **Real-time Price Integration**: Integration with Yahoo Finance for HSI (Hang Seng Index) price data

4. **Data Table Display**: Interactive table showing CBBC details with conditional formatting

## Technical Stack
- **Backend**: Python with Dash framework
- **Database**: PostgreSQL with SQLAlchemy
- **Charting**: Plotly for interactive visualizations
- **Data Processing**: Pandas for data manipulation
- **External APIs**: Yahoo Finance (yfinance) for price data

## Key Features
- Multiple chart visualization modes
- Real-time price overlay on charts
- Conditional table formatting based on price ranges
- Image export functionality for charts
- Database integration for persistent data storage

## Current Status
- Application is functional and running
- Chart types are implemented
- Refresh functionality is working
- All identified bugs have been fixed

## Recent Fixes Applied
1. **Fixed 'osval' chart type bug**: Corrected data source from `idf.OS_VAL, idf.CELL_RANGE` to `df_cum.OS_VAL, df_cum.CallLv`
2. **Fixed download path**: Changed from absolute path `/download/` to relative path `./download/`
3. **Created download directory**: Ensured the download directory exists for image exports
4. **Fixed getPrice function**: Added proper return value (None) when ticker is not found
5. **Enhanced error handling**: Added null check for getPrice return value to prevent crashes

## Enhancement Plan (4 Phases)
The user has defined a 4-phase enhancement plan for visualization features:

### Phase 1: Enhanced Visualization
- ✅ User-configurable scale ratio for side-by-side charts (COMPLETED)
- ✅ Automatic scale detection with intelligent ratio selection (COMPLETED)
- ✅ Y-axis display on right side for side-by-side charts (COMPLETED)
- ✅ Dynamic UI controls for scale ratio selection (COMPLETED)
- Additional chart types (time series, heatmaps)

### Phase 2: Data Analysis
- Statistical indicators
- Trend detection
- Historical comparison

### Phase 3: User Experience
- Tooltips and help text
- User preferences saving
- Mobile optimization

### Phase 4: Performance
- Caching for faster chart rendering
- Optimized data loading
