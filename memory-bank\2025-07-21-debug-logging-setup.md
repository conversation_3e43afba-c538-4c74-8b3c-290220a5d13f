# Setting Log Level to DEBUG in CBBC Application

## Overview
The CBBC application uses a sophisticated logging system with Hong Kong timezone support, daily rotating log files, and multiple logger instances for different components.

## Current Configuration

### ✅ **CONFIGURED**: Log Level Set to DEBUG

The application is now configured to use DEBUG level logging through the following changes:

1. **Environment Variable**: `LOG_LEVEL=DEBUG` in `.env` file
2. **Application Code**: Modified `app.py` to use the environment variable
3. **Logging Configuration**: Updated `logging_config.py` to respect log level settings

## How to Set Log Level

### Method 1: Environment Variable (Recommended)
```bash
# Set in .env file (already done)
LOG_LEVEL=DEBUG

# Or set in terminal session
export LOG_LEVEL=DEBUG
```

### Method 2: Supported Log Levels
- `DEBUG` - Most verbose, shows all messages
- `INFO` - Standard level, shows informational messages and above
- `WARNING` - Shows warnings, errors, and critical messages
- `ERROR` - Shows only errors and critical messages
- `CRITICAL` - Shows only critical messages

## Verification

### Console Output
When running the application, you should see DEBUG messages like:
```
[2025-07-21 20:10:42 HKT] DEBUG    [main_app] 🐛 DEBUG: This is a debug message
[2025-07-21 20:10:42 HKT] INFO     [main_app] ℹ️  INFO: Application starting up
```

### Log Files
DEBUG messages are written to daily rotating log files in the `logs/` directory:
- `logs/YYMMDD_main_app.log` - Main application logs
- `logs/YYMMDD_market_status_worker.log` - Market status checking logs  
- `logs/YYMMDD_auto_update_worker.log` - Auto-update worker logs
- `logs/YYMMDD_hkex_scraper.log` - HKEX scraping logs (when used)

### Test Script
Run the test script to verify logging configuration:
```bash
cd /c/dev/cbbc2
PYTHONPATH=. python test-dev/test_logging_debug.py
```

## Logger Components

### Main Application Logger
```python
main_logger = logging.getLogger('main_app')
main_logger.debug('Debug message')
```

### Specialized Loggers
```python
# Market status worker
market_logger = get_logger('market_status_worker')

# Auto-update worker  
auto_update_logger = get_logger('auto_update_worker')

# HKEX scraper
scraper_logger = get_logger('hkex_scraper')
```

## Features

### ✅ Hong Kong Timezone Support
All timestamps are automatically converted to Hong Kong time (UTC+8)

### ✅ Daily Rotating Files
Log files rotate daily with YYMMDD format (e.g., `250721_main_app.log`)

### ✅ Component Separation
Each component writes to its own log file while also showing on console

### ✅ Configurable Levels
Log level can be set via environment variable or programmatically

## Usage in Code

### Adding Debug Messages
```python
# Use debug for detailed troubleshooting information
logger.debug(f'Processing {len(data)} records')

# Use info for general information
logger.info('Data processing completed successfully')

# Use warning for potential issues
logger.warning('Market API response time exceeded 5 seconds')

# Use error for actual problems
logger.error(f'Failed to connect to database: {error}')
```

## Troubleshooting

### If DEBUG messages don't appear:
1. Check `.env` file has `LOG_LEVEL=DEBUG`
2. Ensure `load_dotenv()` is called before logging setup
3. Verify the log level is being passed to `setup_logging()`
4. Check that the logger level matches the handler level

### If log files are missing:
1. Ensure `logs/` directory exists and is writable
2. Check file permissions
3. Verify the log path configuration

## Current Status

✅ **READY**: DEBUG logging is now active and working correctly
- Environment variable: `LOG_LEVEL=DEBUG` ✅
- Application code: Modified to use environment variable ✅  
- Logging configuration: Updated to respect log levels ✅
- Test verification: All tests passing ✅
- Log files: DEBUG messages being written ✅
- Console output: DEBUG messages showing ✅

You can now run your CBBC application and see detailed DEBUG messages both in the console and in the log files.
