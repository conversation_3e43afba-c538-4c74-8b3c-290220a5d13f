import time
import os

from selenium import webdriver

options = webdriver.ChromeOptions()
options.add_argument('--ignore-ssl-errors=yes')
options.add_argument('--ignore-certificate-errors')
options.add_argument('--disable-dev-shm-usage')


def main():
    print("Start")

    # Get Selenium Hub URL from environment variable
    selenium_hub_url = os.environ.get('SELENIUM_HUB_URL', 'http://localhost:4444/wd/hub')
    print(f"Connecting to Selenium Hub at: {selenium_hub_url}")

    driver = None
    try:
        driver = webdriver.Remote(
            command_executor=selenium_hub_url,
            options=options
        )

        driver.get("https://google.com")
        time.sleep(5)
    finally:
        if driver:
            driver.close()
            driver.quit()

    print("Done")

if __name__ == "__main__":
    main()
