#!/bin/bash

# HKEX Daily Report Scraper - Automation Script
# This script automates the entire process of scraping HKEX daily reports

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
CONTAINER_NAME="cbbc-app"
SELENIUM_CONTAINER="cbbc-selenium"
DEFAULT_URL="https://www.hkex.com.hk/eng/stat/dmstat/dayrpt/dqe250718.htm"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if containers are running
check_containers() {
    log_info "Checking container status..."
    
    # Check app container
    if ! docker ps --format '{{.Names}}' | grep -q "^${CONTAINER_NAME}$"; then
        log_error "Container '${CONTAINER_NAME}' is not running"
        log_info "Please start the application stack first:"
        log_info "  ./scripts/deploy.sh"
        return 1
    fi
    
    # Check selenium container
    if ! docker ps --format '{{.Names}}' | grep -q "^${SELENIUM_CONTAINER}$"; then
        log_error "Container '${SELENIUM_CONTAINER}' is not running"
        log_info "Please start the application stack first:"
        log_info "  ./scripts/deploy.sh"
        return 1
    fi
    
    log_success "Both containers are running"
    return 0
}

# Test selenium connectivity
test_selenium() {
    log_info "Testing Selenium connectivity..."
    
    if docker exec ${CONTAINER_NAME} python test_selenium_connection.py > /tmp/selenium_test.log 2>&1; then
        log_success "Selenium connectivity test passed"
        return 0
    else
        log_error "Selenium connectivity test failed"
        log_info "Test output:"
        cat /tmp/selenium_test.log
        return 1
    fi
}

# Run the scraper
run_scraper() {
    local url="$1"
    local script_name="$2"
    
    log_info "Running HKEX scraper..."
    log_info "Script: ${script_name}"
    log_info "URL: ${url}"
    
    if [[ "$script_name" == "simple" ]]; then
        # Use the simple scraper
        if docker exec ${CONTAINER_NAME} python scrape_hkex_simple.py; then
            log_success "Scraping completed successfully"
            return 0
        else
            log_error "Scraping failed"
            return 1
        fi
    else
        # Use the advanced scraper
        if docker exec ${CONTAINER_NAME} python scrape_hkex_daily_report.py --url "$url" --verbose; then
            log_success "Scraping completed successfully"
            return 0
        else
            log_error "Scraping failed"
            return 1
        fi
    fi
}

# Show output files
show_output() {
    log_info "Checking output files..."
    
    if docker exec ${CONTAINER_NAME} ls -la /app/downloads/ | grep -E "\.(html|json|txt)$"; then
        log_success "Output files found"
        
        echo ""
        log_info "Latest files:"
        docker exec ${CONTAINER_NAME} ls -lt /app/downloads/ | head -10
        
        echo ""
        log_info "File sizes:"
        docker exec ${CONTAINER_NAME} du -h /app/downloads/*.{html,json,txt} 2>/dev/null || true
        
    else
        log_warning "No output files found"
    fi
}

# Show file content preview
show_preview() {
    log_info "Content preview..."
    
    # Find the latest HTML file
    local latest_html=$(docker exec ${CONTAINER_NAME} ls -t /app/downloads/*.html 2>/dev/null | head -1 || echo "")
    
    if [[ -n "$latest_html" ]]; then
        echo ""
        log_info "Preview of: $latest_html"
        echo "----------------------------------------"
        docker exec ${CONTAINER_NAME} head -20 "${latest_html}"
        echo "----------------------------------------"
        
        # Show metadata if available
        local metadata_file="${latest_html%.*}_metadata.json"
        if docker exec ${CONTAINER_NAME} test -f "$metadata_file" 2>/dev/null; then
            echo ""
            log_info "Metadata summary:"
            docker exec ${CONTAINER_NAME} python -c "
import json
try:
    with open('$metadata_file', 'r') as f:
        data = json.load(f)
    print(f'Title: {data.get(\"title\", \"N/A\")}')
    print(f'URL: {data.get(\"url\", \"N/A\")}')
    print(f'Page Size: {data.get(\"page_size\", 0):,} characters')
    print(f'Tables Found: {data.get(\"table_count\", 0)}')
    print(f'Scraped At: {data.get(\"scraped_at\", \"N/A\")}')
except Exception as e:
    print(f'Error reading metadata: {e}')
" 2>/dev/null || echo "Could not read metadata"
        fi
    else
        log_warning "No HTML files found for preview"
    fi
}

# Clean output directory
clean_output() {
    log_info "Cleaning output directory..."
    
    if docker exec ${CONTAINER_NAME} rm -f /app/downloads/*.{html,json,txt} 2>/dev/null; then
        log_success "Output directory cleaned"
    else
        log_warning "Nothing to clean or error occurred"
    fi
}

# Show help
show_help() {
    echo "HKEX Daily Report Scraper - Automation Script"
    echo ""
    echo "Usage: $0 [OPTIONS] [URL]"
    echo ""
    echo "Options:"
    echo "  -s, --simple         Use simple scraper (ignores URL parameter)"
    echo "  -t, --test-only      Only test connectivity, don't scrape"
    echo "  -c, --clean          Clean output directory before scraping"
    echo "  -p, --preview        Show preview of scraped content"
    echo "  -h, --help           Show this help"
    echo ""
    echo "URL:"
    echo "  Custom URL to scrape (default: $DEFAULT_URL)"
    echo ""
    echo "Examples:"
    echo "  $0                                          # Scrape default URL"
    echo "  $0 --simple                                 # Use simple scraper"
    echo "  $0 --clean                                  # Clean then scrape"
    echo "  $0 --test-only                              # Test connectivity only"
    echo "  $0 https://www.hkex.com.hk/eng/stat/...     # Custom URL"
    echo ""
}

# Main function
main() {
    local url="$DEFAULT_URL"
    local use_simple=false
    local test_only=false
    local clean_first=false
    local show_preview_flag=false
    
    # Parse arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            -s|--simple)
                use_simple=true
                shift
                ;;
            -t|--test-only)
                test_only=true
                shift
                ;;
            -c|--clean)
                clean_first=true
                shift
                ;;
            -p|--preview)
                show_preview_flag=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            http*)
                url="$1"
                shift
                ;;
            *)
                log_error "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    echo "======================================================"
    echo "HKEX DAILY REPORT SCRAPER"
    echo "======================================================"
    
    # Check containers
    if ! check_containers; then
        exit 1
    fi
    
    # Clean if requested
    if [[ "$clean_first" == true ]]; then
        clean_output
    fi
    
    # Test connectivity
    if ! test_selenium; then
        exit 1
    fi
    
    # Exit if test-only
    if [[ "$test_only" == true ]]; then
        log_success "Test completed successfully"
        exit 0
    fi
    
    # Run scraper
    local script_type="advanced"
    if [[ "$use_simple" == true ]]; then
        script_type="simple"
    fi
    
    if ! run_scraper "$url" "$script_type"; then
        exit 1
    fi
    
    # Show output
    show_output
    
    # Show preview if requested
    if [[ "$show_preview_flag" == true ]]; then
        show_preview
    fi
    
    echo ""
    echo "======================================================"
    log_success "HKEX scraping completed successfully!"
    echo "======================================================"
    echo ""
    log_info "Next steps:"
    echo "  • View files: docker exec ${CONTAINER_NAME} ls -la /app/downloads/"
    echo "  • Read content: docker exec ${CONTAINER_NAME} cat /app/downloads/hkex_*.txt"
    echo "  • Access via browser: Check shared volume mounts"
    echo ""
}

# Run main function
main "$@"
