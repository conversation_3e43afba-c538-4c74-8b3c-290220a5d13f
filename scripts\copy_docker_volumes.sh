#!/bin/bash

# CBBC Docker Volume Access Script
# This script helps you copy files from Docker volumes to your local directory

set -e

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${BLUE}CBBC Docker Volume Access Tool${NC}"
echo "======================================"

# Create local backup directories
mkdir -p downloads_backup
mkdir -p data_backup
mkdir -p logs_backup
mkdir -p instance_backup

echo -e "${YELLOW}Copying files from Docker volumes...${NC}"

# Copy downloads (HKEX scraped files)
echo "📊 Copying downloads (scraped files)..."
docker run --rm -v cbbc2_selenium_downloads:/data -v $(pwd):/backup alpine sh -c "cp -r /data/* /backup/downloads_backup/ 2>/dev/null || true"

# Copy app data
echo "📁 Copying app data..."
docker run --rm -v cbbc2_app_data:/data -v $(pwd):/backup alpine sh -c "cp -r /data/* /backup/data_backup/ 2>/dev/null || true"

# Copy logs
echo "📋 Copying logs..."
docker run --rm -v cbbc2_app_logs:/data -v $(pwd):/backup alpine sh -c "cp -r /data/* /backup/logs_backup/ 2>/dev/null || true"

# Copy instance (database)
echo "🗄️  Copying database..."
docker run --rm -v cbbc2_app_instance:/data -v $(pwd):/backup alpine sh -c "cp -r /data/* /backup/instance_backup/ 2>/dev/null || true"

echo ""
echo -e "${GREEN}✅ Files copied successfully!${NC}"
echo ""
echo "📁 Your files are now available in:"
echo "   • downloads_backup/  - HKEX scraped files and AASTOCKS data"
echo "   • data_backup/       - Application data"
echo "   • logs_backup/       - Application logs"
echo "   • instance_backup/   - SQLite database"
echo ""
echo "📊 Current downloads:"
ls -la downloads_backup/ 2>/dev/null || echo "   (no files yet)"
echo ""
echo "🔄 To access Docker volumes directly in Windows Explorer:"
echo "   Copy this path: \\\\wsl\$\\docker-desktop-data\\data\\docker\\volumes\\cbbc2_selenium_downloads\\_data"
echo ""
