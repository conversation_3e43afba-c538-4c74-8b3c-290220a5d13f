#!/bin/bash

# CBBC Application Docker Push Script
# This script pushes the Docker image to DockerHub as a private repository

set -e  # Exit on any error

# Configuration
DOCKER_USERNAME="${DOCKER_USERNAME:-breyton}"
IMAGE_NAME="${IMAGE_NAME:-cbbc-app}"
VERSION="${VERSION:-latest}"
PUSH_LATEST="${PUSH_LATEST:-true}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Dock<PERSON> is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        log_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
    log_info "Docker is running"
}

# Check if user is logged in to DockerHub
check_docker_login() {
    if ! docker info | grep -q "Username:"; then
        log_warning "Not logged in to DockerHub. Attempting to log in..."
        if ! docker login; then
            log_error "Failed to log in to DockerHub. Please run 'docker login' manually."
            exit 1
        fi
    fi
    log_info "Logged in to DockerHub"
}

# Check if image exists locally
check_image_exists() {
    local full_image_name="${DOCKER_USERNAME}/${IMAGE_NAME}:${VERSION}"
    
    if ! docker images --format "{{.Repository}}:{{.Tag}}" | grep -q "^${full_image_name}$"; then
        log_error "Image ${full_image_name} not found locally."
        log_info "Please build the image first using: ./scripts/build.sh"
        exit 1
    fi
    log_info "Image ${full_image_name} found locally"
}

# Create private repository on DockerHub (if it doesn't exist)
create_private_repo() {
    local repo_name="${DOCKER_USERNAME}/${IMAGE_NAME}"
    
    log_info "Checking if repository ${repo_name} exists on DockerHub..."
    
    # Note: This requires DockerHub API access and authentication
    # For now, we'll just inform the user to create it manually if needed
    log_warning "Please ensure the repository ${repo_name} exists on DockerHub and is set to private."
    log_info "You can create it at: https://hub.docker.com/repository/create"
    log_info "Make sure to set the visibility to 'Private'"
    
    read -p "Press Enter to continue once the repository is ready..."
}

# Push the Docker image
push_image() {
    local full_image_name="${DOCKER_USERNAME}/${IMAGE_NAME}:${VERSION}"
    
    log_info "Pushing Docker image: ${full_image_name}"
    
    if docker push "${full_image_name}"; then
        log_success "Successfully pushed: ${full_image_name}"
        
        # Push latest tag if requested and version is not latest
        if [ "${PUSH_LATEST}" = "true" ] && [ "${VERSION}" != "latest" ]; then
            local latest_image="${DOCKER_USERNAME}/${IMAGE_NAME}:latest"
            log_info "Pushing latest tag: ${latest_image}"
            
            if docker push "${latest_image}"; then
                log_success "Successfully pushed: ${latest_image}"
            else
                log_error "Failed to push latest tag"
                return 1
            fi
        fi
        
        return 0
    else
        log_error "Failed to push Docker image"
        return 1
    fi
}

# Show push information
show_push_info() {
    local repo_name="${DOCKER_USERNAME}/${IMAGE_NAME}"
    
    log_success "Push completed successfully!"
    log_info "Repository: https://hub.docker.com/r/${repo_name}"
    log_info "Pull command: docker pull ${repo_name}:${VERSION}"
    
    if [ "${PUSH_LATEST}" = "true" ]; then
        log_info "Latest tag: docker pull ${repo_name}:latest"
    fi
}

# Main execution
main() {
    log_info "Starting CBBC Application Docker push process"
    log_info "Configuration:"
    log_info "  Docker Username: ${DOCKER_USERNAME}"
    log_info "  Image Name: ${IMAGE_NAME}"
    log_info "  Version: ${VERSION}"
    log_info "  Push Latest: ${PUSH_LATEST}"
    
    # Check prerequisites
    check_docker
    check_docker_login
    check_image_exists
    
    # Create repository if needed
    create_private_repo
    
    # Push the image
    if push_image; then
        show_push_info
    else
        log_error "Push process failed!"
        exit 1
    fi
}

# Help function
show_help() {
    echo "CBBC Application Docker Push Script"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -u, --username USERNAME    DockerHub username (default: your-dockerhub-username)"
    echo "  -n, --name IMAGE_NAME      Image name (default: cbbc-app)"
    echo "  -v, --version VERSION      Image version/tag (default: latest)"
    echo "  --no-latest               Don't push latest tag"
    echo "  -h, --help                Show this help message"
    echo ""
    echo "Environment variables:"
    echo "  DOCKER_USERNAME           DockerHub username"
    echo "  IMAGE_NAME                Image name"
    echo "  VERSION                   Image version/tag"
    echo "  PUSH_LATEST               Push latest tag (true/false)"
    echo ""
    echo "Examples:"
    echo "  $0                                           # Push with defaults"
    echo "  $0 -u myusername -v v1.0.0                  # Push with specific username and version"
    echo "  DOCKER_USERNAME=myuser VERSION=v1.0.0 $0    # Push with environment variables"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -u|--username)
            DOCKER_USERNAME="$2"
            shift 2
            ;;
        -n|--name)
            IMAGE_NAME="$2"
            shift 2
            ;;
        -v|--version)
            VERSION="$2"
            shift 2
            ;;
        --no-latest)
            PUSH_LATEST="false"
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Validate required parameters
if [ "${DOCKER_USERNAME}" = "your-dockerhub-username" ]; then
    log_error "Please set DOCKER_USERNAME environment variable or use -u option."
    exit 1
fi

# Run main function
main
