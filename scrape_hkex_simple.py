#!/usr/bin/env python3
"""
Simple script to scrape the specific HKEX URL: 
https://www.hkex.com.hk/eng/stat/dmstat/dayrpt/dqe250718.htm

This is a simplified version that demonstrates how to use the scraper
for the specific URL you mentioned.
"""

import sys
import os
from scrape_hkex_daily_report import HKEXDailyReportScraper

def main():
    """Main function to scrape the specific HKEX URL"""
    
    # The specific URL you mentioned
    target_url = "https://www.hkex.com.hk/eng/stat/dmstat/dayrpt/dqe250718.htm"
    
    print("="*60)
    print("HKEX Daily Report Scraper")
    print("="*60)
    print(f"Target URL: {target_url}")
    print("="*60)
    
    # Initialize scraper
    scraper = HKEXDailyReportScraper()
    
    try:
        # Setup WebDriver
        print("1. Setting up WebDriver...")
        if not scraper.setup_webdriver():
            print("ERROR: Failed to setup WebDriver")
            sys.exit(1)
        print("   ✓ WebDriver ready")
        
        # Scrape the page
        print("2. Scraping the page...")
        scraped_data = scraper.scrape_page(target_url)
        print(f"   ✓ Successfully scraped {scraped_data['page_size']:,} characters")
        
        # Save the data
        print("3. Saving data...")
        saved_files = scraper.save_data(scraped_data, "hkex_dqe250718")
        print("   ✓ Data saved successfully")
        
        # Display results
        print("\n" + "="*60)
        print("SCRAPING RESULTS")
        print("="*60)
        print(f"URL: {scraped_data['url']}")
        print(f"Page Title: {scraped_data['title']}")
        print(f"Content Size: {scraped_data['page_size']:,} characters")
        print(f"Tables Found: {scraped_data['table_count']}")
        print(f"Scraped At: {scraped_data['scraped_at']}")
        
        print(f"\nSaved Files:")
        for file_type, path in saved_files.items():
            file_size = os.path.getsize(path) if os.path.exists(path) else 0
            print(f"  {file_type.upper()}: {path} ({file_size:,} bytes)")
        
        print("\n" + "="*60)
        print("SUCCESS: Scraping completed!")
        print("="*60)
        
    except Exception as e:
        print(f"\nERROR: {e}")
        sys.exit(1)
    finally:
        scraper.cleanup()

if __name__ == "__main__":
    main()
