#!/bin/bash

# CBBC Application Deployment Script
# This script deploys the application using Docker Compose

set -e  # Exit on any error

# Configuration
DOCKER_USERNAME="${DOCKER_USERNAME:-breyton}"
IMAGE_NAME="${IMAGE_NAME:-cbbc-app}"
VERSION="${VERSION:-latest}"
COMPOSE_FILE="${COMPOSE_FILE:-docker-compose.prod.yml}"
ENV_FILE="${ENV_FILE:-.env.prod}"
PROJECT_NAME="${PROJECT_NAME:-cbbc}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker and Docker Compose are available
check_prerequisites() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed or not in PATH"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose is not installed or not in PATH"
        exit 1
    fi
    
    if ! docker info > /dev/null 2>&1; then
        log_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
    
    log_info "Prerequisites check passed"
}

# Check if required files exist
check_files() {
    if [ ! -f "${COMPOSE_FILE}" ]; then
        log_error "Docker Compose file not found: ${COMPOSE_FILE}"
        exit 1
    fi
    
    if [ ! -f "${ENV_FILE}" ]; then
        log_warning "Environment file not found: ${ENV_FILE}"
        if [ -f ".env.example" ]; then
            log_info "Copying .env.example to ${ENV_FILE}"
            cp .env.example "${ENV_FILE}"
            log_warning "Please review and update ${ENV_FILE} with your configuration"
        else
            log_info "Using default environment variables from docker-compose"
        fi
    fi
    
    log_info "Required files check passed"
}

# Pull the latest image
pull_image() {
    local full_image_name="${DOCKER_USERNAME}/${IMAGE_NAME}:${VERSION}"
    
    log_info "Pulling latest image: ${full_image_name}"
    
    if docker pull "${full_image_name}"; then
        log_success "Successfully pulled: ${full_image_name}"
    else
        log_error "Failed to pull image. Please check if the image exists and you have access."
        exit 1
    fi
}

# Deploy the application
deploy() {
    log_info "Deploying CBBC application stack..."
    log_info "Services to be deployed:"
    log_info "  - cbbc-app: Main application (Dash/Flask)"
    log_info "  - cbbc-selenium: Selenium Chrome for web scraping"

    # Set environment variables for docker-compose
    export DOCKER_USERNAME
    export IMAGE_NAME
    export VERSION
    
    # Use docker-compose or docker compose based on availability
    local compose_cmd="docker-compose"
    if ! command -v docker-compose &> /dev/null; then
        compose_cmd="docker compose"
    fi
    
    # Deploy with docker-compose
    local compose_args="-f ${COMPOSE_FILE} -p ${PROJECT_NAME}"
    
    if [ -f "${ENV_FILE}" ]; then
        compose_args="${compose_args} --env-file ${ENV_FILE}"
    fi
    
    log_info "Running: ${compose_cmd} ${compose_args} up -d"
    
    if ${compose_cmd} ${compose_args} up -d; then
        log_success "Application stack deployed successfully!"
        log_info "Services deployed:"
        log_info "  - Application: http://localhost:8000"
        log_info "  - Selenium Grid: http://localhost:4444"
        log_info "  - Selenium VNC: http://localhost:7900 (for debugging)"
    else
        log_error "Deployment failed!"
        exit 1
    fi
}

# Show deployment status
show_status() {
    local compose_cmd="docker-compose"
    if ! command -v docker-compose &> /dev/null; then
        compose_cmd="docker compose"
    fi
    
    log_info "Deployment status:"
    ${compose_cmd} -f "${COMPOSE_FILE}" -p "${PROJECT_NAME}" ps

    log_info "Application logs (last 20 lines):"
    ${compose_cmd} -f "${COMPOSE_FILE}" -p "${PROJECT_NAME}" logs --tail=20 app

    log_info "Selenium logs (last 10 lines):"
    ${compose_cmd} -f "${COMPOSE_FILE}" -p "${PROJECT_NAME}" logs --tail=10 selenium
}

# Stop the application
stop() {
    local compose_cmd="docker-compose"
    if ! command -v docker-compose &> /dev/null; then
        compose_cmd="docker compose"
    fi
    
    log_info "Stopping CBBC application..."
    ${compose_cmd} -f "${COMPOSE_FILE}" -p "${PROJECT_NAME}" down
    log_success "Application stopped"
}

# Show logs
show_logs() {
    local compose_cmd="docker-compose"
    if ! command -v docker-compose &> /dev/null; then
        compose_cmd="docker compose"
    fi
    
    local service="${1:-app}"
    local lines="${2:-50}"
    
    log_info "Showing logs for service: ${service} (last ${lines} lines)"
    ${compose_cmd} -f "${COMPOSE_FILE}" -p "${PROJECT_NAME}" logs --tail="${lines}" -f "${service}"
}

# Main execution
main() {
    log_info "Starting CBBC Application deployment process"
    log_info "Configuration:"
    log_info "  Docker Username: ${DOCKER_USERNAME}"
    log_info "  Image Name: ${IMAGE_NAME}"
    log_info "  Version: ${VERSION}"
    log_info "  Compose File: ${COMPOSE_FILE}"
    log_info "  Environment File: ${ENV_FILE}"
    log_info "  Project Name: ${PROJECT_NAME}"
    
    # Check prerequisites
    check_prerequisites
    check_files
    
    # Pull and deploy
    pull_image
    deploy
    
    # Show status
    sleep 5  # Wait a bit for containers to start
    show_status
    
    log_success "Deployment completed!"
    log_info "Application should be available at: http://localhost:8000"
    log_info "Use './scripts/deploy.sh logs' to view logs"
    log_info "Use './scripts/deploy.sh stop' to stop the application"
}

# Help function
show_help() {
    echo "CBBC Application Deployment Script"
    echo ""
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  deploy                     Deploy the application (default)"
    echo "  stop                       Stop the application"
    echo "  status                     Show deployment status"
    echo "  logs [service] [lines]     Show logs (default: app service, 50 lines)"
    echo ""
    echo "Options:"
    echo "  -u, --username USERNAME    DockerHub username"
    echo "  -n, --name IMAGE_NAME      Image name"
    echo "  -v, --version VERSION      Image version/tag"
    echo "  -f, --file COMPOSE_FILE    Docker Compose file"
    echo "  -e, --env ENV_FILE         Environment file"
    echo "  -p, --project PROJECT_NAME Project name"
    echo "  -h, --help                 Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                         # Deploy with defaults"
    echo "  $0 deploy -u myuser -v v1.0.0"
    echo "  $0 logs app 100            # Show last 100 lines of app logs"
    echo "  $0 stop                    # Stop the application"
}

# Parse command line arguments
COMMAND="deploy"
if [[ $# -gt 0 ]] && [[ ! "$1" =~ ^- ]]; then
    COMMAND="$1"
    shift
fi

while [[ $# -gt 0 ]]; do
    case $1 in
        -u|--username)
            DOCKER_USERNAME="$2"
            shift 2
            ;;
        -n|--name)
            IMAGE_NAME="$2"
            shift 2
            ;;
        -v|--version)
            VERSION="$2"
            shift 2
            ;;
        -f|--file)
            COMPOSE_FILE="$2"
            shift 2
            ;;
        -e|--env)
            ENV_FILE="$2"
            shift 2
            ;;
        -p|--project)
            PROJECT_NAME="$2"
            shift 2
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            # For logs command, treat as service name and lines
            if [ "$COMMAND" = "logs" ]; then
                if [ -z "$LOG_SERVICE" ]; then
                    LOG_SERVICE="$1"
                elif [ -z "$LOG_LINES" ]; then
                    LOG_LINES="$1"
                fi
                shift
            else
                log_error "Unknown option: $1"
                show_help
                exit 1
            fi
            ;;
    esac
done

# Execute command
case $COMMAND in
    deploy)
        main
        ;;
    stop)
        stop
        ;;
    status)
        show_status
        ;;
    logs)
        show_logs "${LOG_SERVICE:-app}" "${LOG_LINES:-50}"
        ;;
    *)
        log_error "Unknown command: $COMMAND"
        show_help
        exit 1
        ;;
esac
